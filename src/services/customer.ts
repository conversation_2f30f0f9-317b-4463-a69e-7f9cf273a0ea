import { ApiListResponse, ApiResponse } from "../types/api";
import {
  CustomersInfoItem,
  SaleInfo,
  CustomerFormData,
  ContactInfo,
  ContactFormData,
  CustomerApproveHistoryItem,
} from "../types/customer";
import api from "./api";
import {
  CustomerSimpleInfo,
  AccountSeqInfo,
  AccountSeqSimpleInfo,
} from "../types/customer";

export interface CustomersInfoParams {
  page?: number;
  pageSize?: number;
  filter?: string;
  filterColumn?: string;
  approve_state?: string;
}

export const getCustomersInfo = async (
  params: CustomersInfoParams
): Promise<ApiListResponse<CustomersInfoItem[]>> => {
  const response = await api.get("/customers-info", { params });
  return response.data;
};

export const getCustomersInfoApprove = async (
  params: CustomersInfoParams
): Promise<ApiListResponse<CustomersInfoItem[]>> => {
  const response = await api.get("/customers-approve", { params });
  return response.data;
};

export const getSales = async (): Promise<SaleInfo[]> => {
  // const response = await api.get("/sales");
  return [
    {
      sale_name: "张三",
    },
    {
      sale_name: "李四",
    },
  ];
};

export const createCustomer = async (data: CustomerFormData): Promise<any> => {
  const response = await api.post("/customers-info", data);
  return response.data;
};

export const updateCustomer = async (
  data: CustomerFormData,
  id: number
): Promise<any> => {
  const response = await api.put(`/customers-info/${id}`, data);
  return response.data;
};

// 获取客户联系人信息
export const getCustomerContactInfo = async (
  customerId: number
): Promise<ApiResponse<ContactFormData>> => {
  const response = await api.get(`/customers/${customerId}/contact-info`);
  return response.data;
};

// 获取客户联系人列表
export const getCustomerContactsList = async (
  customerId: number
): Promise<ApiResponse<ContactInfo[]>> => {
  const response = await api.get(`/customers/${customerId}/contacts-info`);
  return response.data;
};

// 创建客户联系人信息
export const createCustomerContact = async (
  customerId: number,
  data: ContactFormData
): Promise<ApiResponse<ContactFormData>> => {
  const response = await api.post(
    `/customers/${customerId}/contacts-info`,
    data
  );
  return response.data;
};

// 更新客户联系人信息
export const updateCustomerContact = async (
  customerId: number,
  contactId: number,
  data: ContactFormData
): Promise<ApiResponse<ContactFormData>> => {
  const response = await api.put(
    `/customers/${customerId}/contacts-info/${contactId}`,
    data
  );
  return response.data;
};

// 获取客户简单列表（用于下拉框）
export const getCustomersSimpleList = async (): Promise<
  ApiResponse<CustomerSimpleInfo[]>
> => {
  const response = await api.get("/customers-info/simple-list");
  return response.data;
};

// 分账序号查询参数
export interface AccountSeqParams {
  page?: number;
  pageSize?: number;
  filter?: string;
  filterColumn?: string;
  customerId?: number;
}

// 获取分账序号列表
export const getAccountSeqList = async (
  customerId: number | null,
  params: AccountSeqParams
): Promise<ApiListResponse<AccountSeqInfo[]>> => {
  const url = customerId
    ? `/customers/${customerId}/account-seq`
    : "/customers/account-seq";
  const response = await api.get(url, { params });
  return response.data;
};

// 创建分账序号
export interface AccountSeqRequest {
  customer_id: number;
  seq_name: string;
  tax: number;
}

export const createAccountSeq = async (
  data: AccountSeqRequest
): Promise<ApiResponse<AccountSeqInfo>> => {
  const response = await api.post(`/account-seq`, data);
  return response.data;
};

// 更新分账序号
export const updateAccountSeq = async (
  customerId: number,
  seqId: number,
  data: AccountSeqRequest
): Promise<ApiResponse<AccountSeqInfo>> => {
  const response = await api.put(
    `/customers/${customerId}/account-seq/${seqId}`,
    data
  );
  return response.data;
};

// 获取分账序号简单列表
export const getAccountSeqSimpleList = async (
  customerId: number
): Promise<ApiResponse<AccountSeqSimpleInfo[]>> => {
  const response = await api.get(
    `/customers/${customerId}/account-seq/simple-list`
  );
  return response.data;
};

// 提交客户审批
export interface ApproveCustomerRequest {
  id: number;
  action: string;
  reason?: string;
}

export const approveCustomer = async (
  data: ApproveCustomerRequest
): Promise<ApiResponse<any>> => {
  const response = await api.post(`/customers-info/approve`, data);
  return response.data;
};

// 获取客户审批历史记录
export interface ApproveHistoryParams {
  page?: number;
  pageSize?: number;
}

export const getCustomerApproveHistory = async (
  customerId: number,
  params: ApproveHistoryParams = {}
): Promise<ApiListResponse<CustomerApproveHistoryItem[]>> => {
  const response = await api.get(`/customers-info/${customerId}/approve-history`, { params });
  return response.data;
};
