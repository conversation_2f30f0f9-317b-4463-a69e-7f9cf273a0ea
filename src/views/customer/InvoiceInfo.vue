<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useToast } from "primevue/usetoast";
import type {
  InvoiceInfoItem,
  InvoiceFormData,
  InvoiceSearchParams,
} from "../../types/invoice";
import {
  InvoiceType,
  CustomerVerifyType,
  CustomerFareOrder,
  IsUnusualNeed,
  IsOpenCharge,
  PostalType,
} from "../../types/invoice";
import {
  getInvoiceList,
  createInvoice,
  updateInvoice,
  getInvoiceDetail,
  getAccountSeqSimpleList,
} from "../../services/invoice";
import { usePermission } from "../../composables/usePermission";

const toast = useToast();
const loading = ref(false);
const totalRecords = ref(0);
const submitted = ref(false);

// 使用权限管理组合式函数
const { hasOperationPermission, initializeUserInfo } = usePermission();

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});
// 筛选选项
const selectedAccountSeq = ref("");
const selectedCreateUser = ref("");
const selectedCustomerInvoiceName = ref("");

// 发票列表数据
const invoices = ref<InvoiceInfoItem[]>([]);

// 发票状态映射
const invoiceStatusSeverityMap: Record<string, string> = {
  暂存: "secondary",
  待账务审核: "warning",
  审核生效: "success",
  注销: "danger",
};

// 新建/编辑发票相关状态
const invoiceDrawerVisible = ref(false);
const invoiceDetailDialog = ref(false);
const selectedInvoice = ref<InvoiceInfoItem | null>(null);
const isSubmitting = ref(false);

// 发票表单数据
const invoiceForm = ref<InvoiceFormData>({
  id: undefined,
  customer_invoice_name: "",
  customer_deposit_bank: "",
  customer_deposit_bank_sub: "",
  customer_bank_account_name: "",
  customer_bank_account: "",
  customer_tax_number: "",
  customer_invoice_type: InvoiceType.VAT_SPECIAL_TICKET,
  customer_receive_explain: "",
  customer_verify_type: CustomerVerifyType.NO_VERIFY,
  customer_fare_order: CustomerFareOrder.PAYMENT_FIRST,
  is_unusual_need: IsUnusualNeed.NO,
  unusual_need_explain: "",
  is_open_charge: IsOpenCharge.SUSPENDED,
  postal_type: PostalType.EMAIL,
  postal_address: "",
  account_seq: "",
  charge_start_day: new Date(),
  charge_end_day: new Date(),
});

// 加载分账序号简单列表
const accountSeqOptions = ref<{ label: string; value: string }[]>([]);
const loadAccountSeqOptions = async () => {
  try {
    const response = await getAccountSeqSimpleList();
    accountSeqOptions.value = response.data.map((item) => ({
      label: `${item.seq_name}(${item.account_seq})`,
      value: item.account_seq,
    }));
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载分账序号列表失败",
      life: 3000,
    });
  }
};

// 下拉选项
const invoiceTypeOptions = [
  { label: "增值税专票", value: InvoiceType.VAT_SPECIAL_TICKET },
  { label: "增值税普票", value: InvoiceType.VAT_GENERAL_TICKET },
  { label: "invoice", value: InvoiceType.INVOICE },
];

const customerVerifyTypeOptions = [
  { label: "不对账", value: CustomerVerifyType.NO_VERIFY },
  { label: "对账无需确认", value: CustomerVerifyType.VERIFY_NO_CONFIRM },
  {
    label: "对账超期无需确认",
    value: CustomerVerifyType.VERIFY_EXPIRED_NO_CONFIRM,
  },
  { label: "对账要确认", value: CustomerVerifyType.VERIFY_NEED_CONFIRM },
];

const customerFareOrderOptions = [
  { label: "先票后款", value: CustomerFareOrder.TICKET_FIRST },
  { label: "先款后票", value: CustomerFareOrder.PAYMENT_FIRST },
];

const isUnusualNeedOptions = [
  { label: "是", value: IsUnusualNeed.YES },
  { label: "否", value: IsUnusualNeed.NO },
];

const isOpenChargeOptions = [
  { label: "暂停", value: IsOpenCharge.SUSPENDED },
  { label: "开账", value: IsOpenCharge.ACTIVE },
  { label: "终止", value: IsOpenCharge.TERMINATED },
];

const postalTypeOptions = [
  { label: "邮件", value: PostalType.EMAIL },
  { label: "快递", value: PostalType.EXPRESS },
  { label: "邮件且快递", value: PostalType.EMAIL_AND_EXPRESS },
  { label: "无需", value: PostalType.NONE },
];

// 格式化日期
const formatDate = (timestamp: string) => {
  if (!timestamp) return "--";
  const date = new Date(parseInt(timestamp) * 1000);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 加载发票列表
const loadInvoices = async () => {
  try {
    loading.value = true;
    const params: InvoiceSearchParams = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };

    if (selectedAccountSeq.value) {
      params.account_seq = selectedAccountSeq.value;
    }

    if (selectedCreateUser.value) {
      params.create_user = selectedCreateUser.value;
    }

    if (selectedCustomerInvoiceName.value) {
      params.customer_invoice_name = selectedCustomerInvoiceName.value;
    }

    const response = await getInvoiceList(params);
    if (response.code === 200) {
      invoices.value = response.data.records;
      totalRecords.value = response.data.page.total;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: response.message || "加载发票列表失败",
        life: 4000,
      });
    }
  } catch (error) {
    console.error("Failed to load invoice list:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载发票列表失败",
      life: 4000,
    });
  } finally {
    loading.value = false;
  }
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadInvoices();
};

// 搜索
const handleSearch = () => {
  lazyParams.value.page = 1;
  loadInvoices();
};

// 重置筛选条件
const resetFilters = () => {
  selectedAccountSeq.value = "";
  selectedCreateUser.value = "";
  selectedCustomerInvoiceName.value = "";
  lazyParams.value.page = 1;
  loadInvoices();
};

// 新建发票
const createNewInvoice = () => {
  invoiceForm.value = {
    id: undefined,
    customer_invoice_name: "",
    customer_deposit_bank: "",
    customer_deposit_bank_sub: "",
    customer_bank_account_name: "",
    customer_bank_account: "",
    customer_tax_number: "",
    customer_invoice_type: InvoiceType.VAT_SPECIAL_TICKET,
    customer_receive_explain: "",
    customer_verify_type: CustomerVerifyType.NO_VERIFY,
    customer_fare_order: CustomerFareOrder.PAYMENT_FIRST,
    is_unusual_need: IsUnusualNeed.NO,
    unusual_need_explain: "",
    is_open_charge: IsOpenCharge.SUSPENDED,
    postal_type: PostalType.EMAIL,
    postal_address: "",
    account_seq: "",
    charge_start_day: new Date(),
    charge_end_day: new Date(),
  };
  submitted.value = false;
  invoiceDrawerVisible.value = true;
};

// 编辑发票
const editInvoice = async (invoice: InvoiceInfoItem) => {
  try {
    const response = await getInvoiceDetail(invoice.id!);
    if (response.code === 200) {
      const data = response.data;
      invoiceForm.value = {
        ...data,
        charge_start_day: new Date(data.charge_start_day),
        charge_end_day: new Date(data.charge_end_day),
      };
      submitted.value = false;
      invoiceDrawerVisible.value = true;
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载发票详情失败",
      life: 3000,
    });
  }
};

// 查看发票详情
const viewInvoice = async (invoice: InvoiceInfoItem) => {
  try {
    const response = await getInvoiceDetail(invoice.id!);
    if (response.code === 200) {
      selectedInvoice.value = response.data;
      invoiceDetailDialog.value = true;
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载发票详情失败",
      life: 3000,
    });
  }
};

// 保存发票
const saveInvoice = async () => {
  submitted.value = true;
  isSubmitting.value = true;

  // 基本验证
  if (
    !invoiceForm.value.account_seq ||
    !invoiceForm.value.customer_invoice_name ||
    !invoiceForm.value.customer_tax_number ||
    !invoiceForm.value.charge_start_day ||
    !invoiceForm.value.charge_end_day
  ) {
    toast.add({
      severity: "error",
      summary: "验证错误",
      detail: "请填写所有必填字段",
      life: 3000,
    });
    return;
  }

  // 邮寄地址验证
  if (
    (invoiceForm.value.postal_type === "快递" ||
      invoiceForm.value.postal_type === "邮件") &&
    !invoiceForm.value.postal_address
  ) {
    toast.add({
      severity: "error",
      summary: "验证错误",
      detail: "选择快递或邮件时，邮寄地址不能为空",
      life: 3000,
    });
    return;
  }

  // 日期验证
  const startDate =
    invoiceForm.value.charge_start_day instanceof Date
      ? invoiceForm.value.charge_start_day
      : new Date(invoiceForm.value.charge_start_day);
  const endDate =
    invoiceForm.value.charge_end_day instanceof Date
      ? invoiceForm.value.charge_end_day
      : new Date(invoiceForm.value.charge_end_day);

  if (startDate >= endDate) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "开账开始日期必须早于开账结束日期",
      life: 3000,
    });
    return;
  }

  try {
    const formData = {
      ...invoiceForm.value,
      charge_start_day: startDate.toISOString().split("T")[0],
      charge_end_day: endDate.toISOString().split("T")[0],
    };

    if (invoiceForm.value.id) {
      await updateInvoice(invoiceForm.value.id, formData);
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "发票信息更新成功",
        life: 3000,
      });
    } else {
      await createInvoice(formData);
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "发票信息创建成功",
        life: 3000,
      });
    }

    invoiceDrawerVisible.value = false;
    loadInvoices();
  } catch (error: any) {
    if (error.response?.status === 422) {
      toast.add({
        severity: "error",
        summary: "验证错误",
        detail: error.response.data.message || "请检查输入数据",
        life: 3000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: "保存发票信息失败",
        life: 3000,
      });
    }
  } finally {
    isSubmitting.value = false;
  }
};

onMounted(async () => {
  await initializeUserInfo();
  loadAccountSeqOptions();
  loadInvoices();
});
</script>

<template>
  <div class="invoice-container">
    <div class="card">
      <Toolbar class="mb-2">
        <template #start>
          <div class="flex align-items-center gap-3">
            <Select
              v-model="selectedAccountSeq"
              :options="accountSeqOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="请选择分账序号"
              :showClear="true"
              filter
              @change="loadInvoices"
              style="min-width: 250px"
            />
          </div>
        </template>
        <template #end>
          <div class="flex align-items-center gap-2">
            <FloatLabel>
              <InputText v-model="selectedCreateUser" />
              <label>创建者</label>
            </FloatLabel>
            <FloatLabel>
              <InputText v-model="selectedCustomerInvoiceName" />
              <label>发票名称</label>
            </FloatLabel>
            <Button
              label="搜索"
              icon="pi pi-search"
              @click="handleSearch"
              outlined
            />
            <Button
              label="重置"
              icon="pi pi-refresh"
              @click="resetFilters"
              severity="secondary"
              outlined
            />
          </div>
          <Divider layout="vertical" />
          <Button
            label="新建"
            icon="pi pi-plus"
            @click="createNewInvoice"
            :disabled="!hasOperationPermission"
          />
        </template>
      </Toolbar>

      <DataTable
        :value="invoices"
        :loading="loading"
        :lazy="true"
        :paginator="true"
        :rows="20"
        :rowsPerPageOptions="[10, 20, 50]"
        :totalRecords="totalRecords"
        @page="onPage($event)"
        showGridlines
        stripedRows
        scrollable
        scrollHeight="calc(100vh - 22rem)"
      >
        <template #empty>
          <div class="empty-message">
            <i
              class="pi pi-inbox"
              style="
                font-size: 2rem;
                color: var(--p-text-color-secondary);
                margin-bottom: 1rem;
              "
            ></i>
            <p>暂无发票数据</p>
          </div>
        </template>
        <Column
          field="customer_invoice_name"
          header="开票名称"
          style="min-width: 20rem"
        />
        <Column
          field="account_seq"
          header="分账序号"
          style="min-width: 10rem"
        />
        <Column field="status" header="状态" style="min-width: 8rem">
          <template #body="{ data }">
            <Tag
              :severity="invoiceStatusSeverityMap[data.status] || 'info'"
              :value="data.status"
            />
          </template>
        </Column>
        <Column field="create_user" header="创建者" style="min-width: 8rem" />
        <Column field="created_at" header="创建时间" style="min-width: 20rem">
          <template #body="{ data }">
            {{ formatDate(data.created_at) }}
          </template>
        </Column>
        <Column
          header="操作"
          :exportable="false"
          alignFrozen="right"
          frozen
          style="min-width: 15rem"
        >
          <template #body="{ data }">
            <Button
              icon="pi pi-pencil"
              outlined
              class="p-button-rounded p-button-success mr-2"
              @click="editInvoice(data)"
              v-tooltip.top="'编辑发票信息'"
            />
            <Button
              icon="pi pi-eye"
              outlined
              class="p-button-rounded p-button-info"
              @click="viewInvoice(data)"
              v-tooltip.top="'查看发票信息详情'"
            />
          </template>
        </Column>
      </DataTable>

      <!-- 新建/编辑发票信息抽屉 -->
      <Drawer
        v-model:visible="invoiceDrawerVisible"
        position="right"
        :style="{ width: '80rem' }"
        :modal="true"
        :closable="true"
        :dismissable="false"
        :showCloseIcon="true"
        :header="invoiceForm.id ? '编辑发票信息' : '新建发票信息'"
        class="invoice-drawer p-fluid"
      >
        <div class="p-4">
          <!-- 基本信息 -->
          <div class="form-section">
            <div class="section-header">
              <h3 class="section-title">基本信息</h3>
              <Divider />
            </div>
            <div class="section-content">
              <Fluid>
                <div class="grid grid-cols-3 gap-4">
                  <div class="field">
                    <label for="account_seq" class="required">分账序号</label>
                    <Select
                      id="account_seq"
                      v-model="invoiceForm.account_seq"
                      :options="accountSeqOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择分账序号"
                      filter
                      :class="{
                        'p-invalid': submitted && !invoiceForm.account_seq,
                      }"
                    />
                    <small
                      class="p-error"
                      v-if="submitted && !invoiceForm.account_seq"
                      >分账序号不能为空</small
                    >
                  </div>

                  <div class="field">
                    <label for="customer_invoice_name" class="required"
                      >开票名称</label
                    >
                    <InputText
                      id="customer_invoice_name"
                      v-model="invoiceForm.customer_invoice_name"
                      :class="{
                        'p-invalid':
                          submitted && !invoiceForm.customer_invoice_name,
                      }"
                    />
                    <small
                      class="p-error"
                      v-if="submitted && !invoiceForm.customer_invoice_name"
                      >开票名称不能为空</small
                    >
                  </div>

                  <div class="field">
                    <label for="customer_tax_number" class="required"
                      >税号</label
                    >
                    <InputText
                      id="customer_tax_number"
                      v-model="invoiceForm.customer_tax_number"
                      :class="{
                        'p-invalid':
                          submitted && !invoiceForm.customer_tax_number,
                      }"
                    />
                    <small
                      class="p-error"
                      v-if="submitted && !invoiceForm.customer_tax_number"
                      >税号不能为空</small
                    >
                  </div>

                  <div class="field">
                    <label for="customer_invoice_type">发票类型</label>
                    <Select
                      id="customer_invoice_type"
                      v-model="invoiceForm.customer_invoice_type"
                      :options="invoiceTypeOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择发票类型"
                    />
                  </div>

                  <div class="field">
                    <label for="customer_verify_type">对账类型</label>
                    <Select
                      id="customer_verify_type"
                      v-model="invoiceForm.customer_verify_type"
                      :options="customerVerifyTypeOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择对账类型"
                    />
                  </div>

                  <div class="field">
                    <label for="customer_fare_order">票款顺序</label>
                    <Select
                      id="customer_fare_order"
                      v-model="invoiceForm.customer_fare_order"
                      :options="customerFareOrderOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择票款顺序"
                    />
                  </div>
                </div>
              </Fluid>
            </div>
          </div>

          <!-- 银行信息 -->
          <div class="form-section">
            <div class="section-header">
              <h3 class="section-title">银行信息</h3>
              <Divider />
            </div>
            <div class="section-content">
              <Fluid>
                <div class="grid grid-cols-2 gap-4">
                  <div class="field">
                    <label for="customer_deposit_bank">开户银行</label>
                    <InputText
                      id="customer_deposit_bank"
                      v-model="invoiceForm.customer_deposit_bank"
                    />
                  </div>

                  <div class="field">
                    <label for="customer_deposit_bank_sub">开户支行</label>
                    <InputText
                      id="customer_deposit_bank_sub"
                      v-model="invoiceForm.customer_deposit_bank_sub"
                    />
                  </div>

                  <div class="field">
                    <label for="customer_bank_account_name">银行账户名</label>
                    <InputText
                      id="customer_bank_account_name"
                      v-model="invoiceForm.customer_bank_account_name"
                    />
                  </div>

                  <div class="field">
                    <label for="customer_bank_account">银行账号</label>
                    <InputText
                      id="customer_bank_account"
                      v-model="invoiceForm.customer_bank_account"
                    />
                  </div>
                </div>
              </Fluid>
            </div>
          </div>

          <!-- 其他设置 -->
          <div class="form-section">
            <div class="section-header">
              <h3 class="section-title">其他设置</h3>
              <Divider />
            </div>
            <div class="section-content">
              <Fluid>
                <div class="grid grid-cols-3 gap-4">
                  <div class="field">
                    <label for="is_unusual_need">是否有特殊需求</label>
                    <Select
                      id="is_unusual_need"
                      v-model="invoiceForm.is_unusual_need"
                      :options="isUnusualNeedOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择是否有特殊需求"
                    />
                  </div>

                  <div
                    class="field"
                    v-if="invoiceForm.is_unusual_need === '是'"
                  >
                    <label for="unusual_need_explain">特殊需求说明</label>
                    <InputText
                      id="unusual_need_explain"
                      v-model="invoiceForm.unusual_need_explain"
                    />
                  </div>

                  <div class="field">
                    <label for="is_open_charge">开账状态</label>
                    <Select
                      id="is_open_charge"
                      v-model="invoiceForm.is_open_charge"
                      :options="isOpenChargeOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择开账状态"
                    />
                  </div>

                  <div class="field">
                    <label for="postal_type">邮寄方式</label>
                    <Select
                      id="postal_type"
                      v-model="invoiceForm.postal_type"
                      :options="postalTypeOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择邮寄方式"
                    />
                  </div>

                  <div
                    class="field"
                    v-if="
                      invoiceForm.postal_type !== PostalType.NONE
                    "
                  >
                    <label for="postal_address">邮寄地址</label>
                    <InputText
                      id="postal_address"
                      v-model="invoiceForm.postal_address"
                    />
                  </div>

                  <div class="field">
                    <label for="customer_receive_explain">收票说明</label>
                    <InputText
                      id="customer_receive_explain"
                      v-model="invoiceForm.customer_receive_explain"
                    />
                  </div>

                  <div class="field">
                    <label for="charge_start_day" class="required"
                      >开账开始日期</label
                    >
                    <DatePicker
                      id="charge_start_day"
                      v-model="invoiceForm.charge_start_day"
                      dateFormat="yy-mm-dd"
                      showIcon
                      :class="{
                        'p-invalid': submitted && !invoiceForm.charge_start_day,
                      }"
                    />
                    <small
                      class="p-error"
                      v-if="submitted && !invoiceForm.charge_start_day"
                      >开账开始日期不能为空</small
                    >
                  </div>

                  <div class="field">
                    <label for="charge_end_day" class="required"
                      >开账结束日期</label
                    >
                    <DatePicker
                      id="charge_end_day"
                      v-model="invoiceForm.charge_end_day"
                      dateFormat="yy-mm-dd"
                      showIcon
                      :class="{
                        'p-invalid': submitted && !invoiceForm.charge_end_day,
                      }"
                    />
                    <small
                      class="p-error"
                      v-if="submitted && !invoiceForm.charge_end_day"
                      >开账结束日期不能为空</small
                    >
                  </div>
                </div>
              </Fluid>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-content-end gap-2">
            <Button
              label="取消"
              icon="pi pi-times"
              severity="secondary"
              outlined
              @click="invoiceDrawerVisible = false"
              :disabled="isSubmitting"
            />
            <Button
              label="保存"
              icon="pi pi-check"
              @click="saveInvoice"
              :disabled="isSubmitting"
              :loading="isSubmitting"
            />
          </div>
        </template>
      </Drawer>

      <!-- 发票信息详情弹框 -->
      <Dialog
        v-model:visible="invoiceDetailDialog"
        modal
        :header="`发票信息详情 - ${
          selectedInvoice?.customer_invoice_name || ''
        }`"
        :style="{ width: '80rem' }"
        class="apple-invoice-detail-dialog"
        :closable="true"
        :dismissableMask="true"
        maximizable
      >
        <div v-if="selectedInvoice" class="invoice-detail-content">
          <!-- 基本信息 -->
          <div class="detail-section">
            <h3 class="section-title">
              <i class="pi pi-info-circle"></i>
              基本信息
            </h3>
            <Fluid>
              <div class="grid grid-cols-3 gap-3">
                <div class="detail-item">
                  <label>分账序号</label>
                  <span class="detail-value">{{
                    selectedInvoice.account_seq
                  }}</span>
                </div>
                <div class="detail-item">
                  <label>开票名称</label>
                  <span class="detail-value">{{
                    selectedInvoice.customer_invoice_name
                  }}</span>
                </div>
                <div class="detail-item">
                  <label>税号</label>
                  <span class="detail-value">{{
                    selectedInvoice.customer_tax_number
                  }}</span>
                </div>
                <div class="detail-item">
                  <label>发票类型</label>
                  <span class="detail-value">{{
                    selectedInvoice.customer_invoice_type
                  }}</span>
                </div>
                <div class="detail-item">
                  <label>对账类型</label>
                  <span class="detail-value">{{
                    selectedInvoice.customer_verify_type
                  }}</span>
                </div>
                <div class="detail-item">
                  <label>票款顺序</label>
                  <span class="detail-value">{{
                    selectedInvoice.customer_fare_order
                  }}</span>
                </div>
              </div>
            </Fluid>
          </div>

          <!-- 银行信息 -->
          <div class="detail-section">
            <h3 class="section-title">
              <i class="pi pi-building"></i>
              银行信息
            </h3>
            <Fluid>
              <div class="grid grid-cols-3 gap-3">
                <div class="detail-item">
                  <label>开户银行</label>
                  <span class="detail-value">{{
                    selectedInvoice.customer_deposit_bank || "--"
                  }}</span>
                </div>
                <div class="detail-item">
                  <label>开户支行</label>
                  <span class="detail-value">{{
                    selectedInvoice.customer_deposit_bank_sub || "--"
                  }}</span>
                </div>
                <div class="detail-item">
                  <label>银行账户名</label>
                  <span class="detail-value">{{
                    selectedInvoice.customer_bank_account_name || "--"
                  }}</span>
                </div>
                <div class="detail-item">
                  <label>银行账号</label>
                  <span class="detail-value">{{
                    selectedInvoice.customer_bank_account || "--"
                  }}</span>
                </div>
              </div>
            </Fluid>
          </div>

          <!-- 其他设置 -->
          <div class="detail-section">
            <h3 class="section-title">
              <i class="pi pi-cog"></i>
              其他设置
            </h3>
            <Fluid>
              <div class="grid grid-cols-3 gap-3">
                <div class="detail-item">
                  <label>是否有特殊需求</label>
                  <span class="detail-value">{{
                    selectedInvoice.is_unusual_need
                  }}</span>
                </div>
                <div
                  class="detail-item"
                  v-if="selectedInvoice.is_unusual_need === '是'"
                >
                  <label>特殊需求说明</label>
                  <span class="detail-value">{{
                    selectedInvoice.unusual_need_explain || "--"
                  }}</span>
                </div>
                <div class="detail-item">
                  <label>开账状态</label>
                  <span class="detail-value">{{
                    selectedInvoice.is_open_charge
                  }}</span>
                </div>
                <div class="detail-item">
                  <label>邮寄方式</label>
                  <span class="detail-value">{{
                    selectedInvoice.postal_type
                  }}</span>
                </div>
                <div
                  class="detail-item"
                  v-if="
                    selectedInvoice.postal_type === '快递' ||
                    selectedInvoice.postal_type === '邮件'
                  "
                >
                  <label>邮寄地址</label>
                  <span class="detail-value">{{
                    selectedInvoice.postal_address || "--"
                  }}</span>
                </div>
                <div class="detail-item">
                  <label>收票说明</label>
                  <span class="detail-value">{{
                    selectedInvoice.customer_receive_explain || "--"
                  }}</span>
                </div>
                <div class="detail-item">
                  <label>开账开始日期</label>
                  <span class="detail-value">{{
                    selectedInvoice.charge_start_day
                  }}</span>
                </div>
                <div class="detail-item">
                  <label>开账结束日期</label>
                  <span class="detail-value">{{
                    selectedInvoice.charge_end_day
                  }}</span>
                </div>
                <div class="detail-item">
                  <label>状态</label>
                  <Tag
                    :severity="
                      invoiceStatusSeverityMap[selectedInvoice.status || ''] ||
                      'info'
                    "
                    :value="selectedInvoice.status"
                    class="detail-tag"
                  />
                </div>
                <div class="detail-item">
                  <label>创建者</label>
                  <span class="detail-value">{{
                    selectedInvoice.create_user
                  }}</span>
                </div>
                <div class="detail-item">
                  <label>创建时间</label>
                  <span class="detail-value">{{
                    formatDate(selectedInvoice.created_at || "")
                  }}</span>
                </div>
              </div>
            </Fluid>
          </div>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <Button
              label="关闭"
              icon="pi pi-times"
              severity="secondary"
              outlined
              @click="invoiceDetailDialog = false"
              class="apple-button"
            />
          </div>
        </template>
      </Dialog>
    </div>
  </div>
</template>

<style scoped>
.invoice-container {
  padding: 1rem;
  height: calc(100vh - 10rem);
}

.card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
}

/* 表单验证错误样式 */
.p-error {
  color: #ff3b30;
  font-size: 0.75rem;
  font-weight: 400;
  margin-top: 0.25rem;
  display: block;
  line-height: 1.2;
}

:deep(.p-invalid) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 1px #ff3b30 !important;
}

/* 按钮样式 */
:deep(.p-button) {
  border-radius: 8px;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-weight: 500;
}

:deep(.p-button:hover) {
  transform: translateY(-1px);
}

:deep(.p-button:active) {
  transform: scale(0.98);
}

/* 数据表格样式 */
:deep(.p-datatable .p-datatable-thead > tr > th) {
  background: #f8f9fa !important;
  border-bottom: 2px solid #e9ecef !important;
}

:deep(.p-datatable .p-datatable-tbody > tr > td) {
  border-bottom: 1px solid #f1f1f1 !important;
  font-size: 0.9rem !important;
  color: #1d1d1f !important;
  padding: 0.5rem 1rem !important;
}

/* 表单区域样式 */
.form-section {
  margin-bottom: 2rem;
  border-radius: 12px;
  border: 1px solid var(--surface-border);
  overflow: hidden;
}

.section-header {
  padding: 1.5rem 1.5rem 0 1.5rem;
  background: var(--p-surface-card);
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--p-text-color);
  margin: 0 0 1rem 0;
}

.section-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.field {
  margin-bottom: 0.5rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--p-primary-color);
  letter-spacing: -0.025em;
}

.field label.required::after {
  content: " *";
  color: #ff3b30;
  font-weight: 600;
  margin-left: 2px;
}

/* 发票抽屉样式 - 参考OrderList.vue */
:deep(.invoice-drawer) {
  .p-drawer-content {
    display: flex;
    flex-direction: column;
  }
}

/* Apple Design System - 发票详情弹框样式 */
:deep(.apple-invoice-detail-dialog) {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15) !important;
}

:deep(.apple-invoice-detail-dialog .p-dialog-header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 16px 16px 0 0 !important;
}

:deep(.apple-invoice-detail-dialog .p-dialog-title) {
  font-weight: 600 !important;
  font-size: 1.25rem !important;
  color: #1d1d1f !important;
}

:deep(.apple-invoice-detail-dialog .p-dialog-content) {
  padding: 2rem !important;
  background: #ffffff !important;
}

:deep(.apple-invoice-detail-dialog .p-dialog-footer) {
  background: #f8f9fa !important;
  border-top: 1px solid rgba(0, 0, 0, 0.08) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 0 0 16px 16px !important;
}

.invoice-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1d1d1f;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--p-primary-color);
}

.section-title i {
  color: var(--p-primary-color);
  font-size: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item label {
  font-weight: 600;
  font-size: 0.875rem;
  color: #6e6e73;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 1rem;
  color: #1d1d1f;
  font-weight: 500;
  padding: 0.75rem 1rem;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  min-height: 2.5rem;
  display: flex;
  align-items: center;
}

.detail-tag {
  align-self: flex-start;
  font-weight: 500 !important;
  padding: 0.5rem 0.75rem !important;
  border-radius: 12px !important;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.apple-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 0.75rem 1.5rem !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.apple-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 滚动条样式 */
.invoice-detail-content::-webkit-scrollbar {
  width: 6px;
}

.invoice-detail-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.invoice-detail-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.invoice-detail-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
